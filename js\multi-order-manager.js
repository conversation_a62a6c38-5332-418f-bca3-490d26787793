/**
 * 多订单处理管理器 - 重构优化版 v2.0
 * 负责多订单检测、智能分割、批量处理和UI交互管理
 * 重点优化：代码清理、AI分析增强、UI交互改进、批量处理
 * <AUTHOR>
 * @version 2.0.0
 */

// 防止重复加载
if (window.MultiOrderManager) {
    console.log('重构版多订单管理器已存在，跳过重复加载');
} else {

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA?.logger || window.logger;
}

function getGeminiService() {
    return window.OTA?.geminiService || window.geminiService;
}

function getApiService() {
    return window.OTA?.apiService || window.apiService;
}

function getAppState() {
    return window.OTA?.appState || window.appState;
}

function getUiManager() {
    return window.OTA?.uiManager || window.uiManager;
}

class MultiOrderManager {
    constructor() {
        // 1. 配置和模式
        this.config = {
            minInputLength: 50,           // 最小输入长度启动检测
            debounceDelay: 1200,          // 防抖延迟
            maxOrdersPerBatch: 5,         // 每批次最大订单数
            batchDelay: 800,              // 批次间延迟（ms）
            confidenceThreshold: 0.7      // AI检测置信度阈值
        };

        // 2. 状态管理
        this.state = {
            isAnalyzing: false,
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            batchProgress: {
                total: 0,
                completed: 0,
                failed: 0,
                isRunning: false
            }
        };

        // 4. 事件监听器
        this.eventListeners = new Map();
        this.debounceTimer = null;
        
        this.init();
    }

    /**
     * 初始化管理器
     */
    init() {
        const logger = getLogger();
        logger?.log('多订单管理器v2.0正在初始化...', 'info');

        this.setupEventListeners();
        this.setupInputListener();
        this.initPanelEvents();
        
        logger?.log('多订单管理器v2.0初始化完成', 'success');
    }

    /**
     * 设置全局事件监听器
     */
    setupEventListeners() {
        // 保存 this 引用以确保正确的上下文
        const self = this;
        
        // 监听多订单检测事件 - 处理新的统一入口事件格式
        document.addEventListener('multiOrderDetected', (event) => {
            try {
                const { multiOrderResult, orderText } = event.detail;
                getLogger()?.log('🔔 收到多订单检测事件（统一入口）', 'info', { 
                    hasMultiOrderResult: !!multiOrderResult,
                    orderCount: multiOrderResult?.orderCount || 0,
                    isMultiOrder: multiOrderResult?.isMultiOrder || false,
                    hasOrderText: !!orderText 
                });
                self.handleMultiOrderDetectionUnified(multiOrderResult, orderText);
            } catch (error) {
                getLogger()?.logError('多订单事件处理失败', error);
            }
        });

        // 监听应用状态变化
        document.addEventListener('appStateChanged', (event) => {
            try {
                if (event.detail.key === 'currentOrder') {
                    self.handleOrderStateChange(event.detail.value);
                }
            } catch (error) {
                getLogger()?.logError('应用状态变化处理失败', error);
            }
        });

        getLogger()?.log('多订单事件监听器已设置', 'info');
    }

    /**
     * 处理多订单检测事件（统一入口版本）
     * @param {object} multiOrderResult - Gemini返回的完整多订单检测结果
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetectionUnified(multiOrderResult, orderText) {
        const logger = getLogger();
        logger?.log('🔄 处理多订单检测事件（统一入口）', 'info', { 
            isMultiOrder: multiOrderResult?.isMultiOrder,
            orderCount: multiOrderResult?.orderCount || 0,
            confidence: multiOrderResult?.confidence || 0,
            hasOrders: !!multiOrderResult?.orders,
            orderLength: orderText?.length || 0 
        });

        try {
            // 验证输入参数
            if (!multiOrderResult || typeof multiOrderResult !== 'object') {
                throw new Error('多订单检测结果格式无效');
            }

            // 🎯 核心逻辑：根据orderCount决定处理方式
            if (multiOrderResult.orderCount > 1 && multiOrderResult.orders && multiOrderResult.orders.length > 1) {
                // 多订单模式：直接显示多订单面板
                logger?.log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`, 'success');
                
                // 保存多订单状态
                this.state.isMultiOrderMode = true;
                this.state.parsedOrders = multiOrderResult.orders;
                this.state.multiOrderResult = multiOrderResult;
                
                // 显示多订单面板
                this.showMultiOrderPanel(multiOrderResult.orders);
                
                logger?.log('🎉 多订单面板显示成功', 'success');
                
            } else if (multiOrderResult.orderCount === 1) {
                // 单订单模式：隐藏多订单面板（如果之前显示过）
                logger?.log('✅ 确认单订单模式，隐藏多订单面板', 'info');
                this.hideMultiOrderPanel();
                
            } else {
                // 无有效订单：隐藏面板并记录
                logger?.log('⚠️ 无有效订单，隐藏多订单面板', 'warning');
                this.hideMultiOrderPanel();
            }
            
        } catch (error) {
            logger?.logError('处理多订单检测事件失败（统一入口）', error);
            this.hideMultiOrderPanel();
        }
    }

    /**
     * 处理多订单检测事件（旧版本，保留向后兼容）
     * @param {object} data - 订单数据
     * @param {string} orderText - 订单文本
     */
    handleMultiOrderDetection(data, orderText) {
        const logger = getLogger();
        logger?.log('🔄 处理多订单检测事件', 'info', { 
            dataType: Array.isArray(data) ? 'array' : typeof data,
            orderLength: orderText?.length || 0 
        });

        try {
            // 处理不同格式的数据
            let processedData = data;
            
            // 如果是对象，尝试转换为数组
            if (data && typeof data === 'object' && !Array.isArray(data)) {
                // 检查是否有 orders 属性
                if (data.orders && Array.isArray(data.orders)) {
                    processedData = data.orders;
                }
                // 检查是否有 segments 属性
                else if (data.segments && Array.isArray(data.segments)) {
                    processedData = data.segments;
                }
                // 检查是否有 items 属性
                else if (data.items && Array.isArray(data.items)) {
                    processedData = data.items;
                }
                // 如果对象有多个键值对，可能每个键都是一个订单
                else {
                    const keys = Object.keys(data);
                    if (keys.length > 1) {
                        processedData = keys.map(key => ({
                            id: key,
                            ...data[key]
                        }));
                    }
                }
            }

            // 统一的多订单处理逻辑
            if (Array.isArray(processedData) && processedData.length > 1) {
                logger?.log(`检测到${processedData.length}个订单，显示多订单面板`, 'info');
                this.showMultiOrderPanel(processedData);
            } else if (Array.isArray(processedData) && processedData.length === 1) {
                logger?.log('检测到单个订单，进行智能分析', 'info');
                this.analyzeInputForMultiOrder(orderText);
            } else {
                logger?.log('无有效数据或无法识别格式，进行智能分析', 'info');
                // 检查是否包含多个时间点或地点，使用智能分析
                this.analyzeInputForMultiOrder(orderText);
            }
        } catch (error) {
            logger?.logError('处理多订单检测事件失败', error);
        }
    }

    /**
     * 处理订单状态变化
     * @param {object} orderData - 订单数据
     */
    handleOrderStateChange(orderData) {
        const logger = getLogger();
        logger?.log('🔄 处理订单状态变化', 'info', orderData);

        try {
            // 如果有当前订单数据，可以进行额外的处理
            if (orderData) {
                // 这里可以添加订单状态变化的处理逻辑
                // 例如：更新UI显示、保存到历史记录等
                logger?.log('订单状态已更新', 'success');
            }
        } catch (error) {
            logger?.logError('处理订单状态变化失败', error);
        }
    }

    /**
     * 设置输入事件监听器
     */
    setupInputListener() {
        // 确保DOM元素存在后再绑定事件
        const bindEvents = () => {
            const orderInput = document.getElementById('orderInput');
            if (orderInput) {
                this.bindInputEvents();
                getLogger()?.log('✅ 输入事件监听器已成功绑定', 'success');
            } else {
                getLogger()?.log('⚠️ orderInput元素不存在，等待DOM加载...', 'warn');
                // 如果元素不存在，等待一段时间后重试
                setTimeout(bindEvents, 1000);
            }
        };

        // 立即尝试绑定
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', bindEvents);
        } else {
            bindEvents();
        }
    }

    /**
     * 绑定输入事件（优化版）
     */
    bindInputEvents() {
        const orderInput = document.getElementById('orderInput');
        if (!orderInput) {
            getLogger()?.log('订单输入框不存在，跳过事件绑定', 'warn');
            return;
        }

        // 防抖处理的输入事件
        const handleInput = (event) => {
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }
            
            this.debounceTimer = setTimeout(async () => {
                if (this.state.isAnalyzing) return;
                
                try {
                    this.state.isAnalyzing = true;
                    await this.analyzeInputForMultiOrder(event.target.value);
                } catch (error) {
                    getLogger()?.logError('多订单分析失败', error);
                } finally {
                    this.state.isAnalyzing = false;
                }
            }, this.config.debounceDelay);
        };

        // 绑定事件
        orderInput.addEventListener('input', handleInput);
        orderInput.addEventListener('paste', (event) => {
            setTimeout(() => handleInput(event), 100);
        });

        getLogger()?.log('输入事件监听器已绑定', 'info');
    }

    /**
     * 分析输入内容是否为多订单（旧版本，现在统一入口已处理）
     * ⚠️ 注意：此方法现在主要用于向后兼容和手动触发
     * 正常流程应使用实时分析管理器的统一入口处理
     * @param {string} text - 输入文本
     */
    async analyzeInputForMultiOrder(text) {
        if (!text || text.trim().length < this.config.minInputLength) {
            this.hideMultiOrderPanel();
            return;
        }

        const logger = getLogger();
        logger?.log(`🔍 开始分析输入内容，长度: ${text.length}字符`, 'info');
        
        try {
            // 直接调用Gemini服务进行一体化解析
            logger?.log('🤖 使用一体化Gemini AI进行多订单检测和完整解析...', 'info');
            
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            const geminiResult = await geminiService.detectAndSplitMultiOrders(text);
            
            // 添加详细的调试日志
            logger?.log('🔍 Gemini返回结果详情:', 'info', {
                isMultiOrder: geminiResult.isMultiOrder,
                hasOrders: !!geminiResult.orders,
                ordersLength: geminiResult.orders ? geminiResult.orders.length : 0,
                hasSegments: !!geminiResult.segments,
                segmentsLength: geminiResult.segments ? geminiResult.segments.length : 0,
                fullResult: geminiResult
            });
            
            if (geminiResult.isMultiOrder && geminiResult.orders && geminiResult.orders.length > 1) {
                logger?.log(`✅ Gemini检测到多订单: ${geminiResult.orders.length}个完整订单`, 'success', {
                    confidence: geminiResult.confidence,
                    analysis: geminiResult.analysis,
                    orderCount: geminiResult.orderCount
                });
                
                // 传递完整解析的订单对象数组
                logger?.log('📋 准备显示多订单面板...', 'info');
                this.showMultiOrderPanel(geminiResult.orders);
                logger?.log('✅ 多订单面板显示完成', 'success');
            } else {
                logger?.log('📋 Gemini检测为单订单，隐藏多订单面板', 'info', {
                    reason: !geminiResult.isMultiOrder ? '非多订单' : 
                           !geminiResult.orders ? '缺少orders数组' : 
                           geminiResult.orders.length <= 1 ? '订单数量不足' : '未知原因'
                });
                this.hideMultiOrderPanel();
            }
        } catch (error) {
            logger?.logError('Gemini一体化分析失败', error);
            // 异常情况下隐藏面板
            this.hideMultiOrderPanel();
        }
    }

    /**
     * 传统多订单检测（基于模式匹配）
     * @param {string} text - 输入文本
     * @returns {object} 检测结果
     */
    detectMultiOrderTraditional(text) {
        if (!text || typeof text !== 'string') {
            return { isMultiOrder: false, confidence: 0, reason: '文本无效' };
        }

        const cleanText = text.trim();
        let score = 0;
        const reasons = [];

        // 1. 检查明确的订单标识
        const orderMarkers = [
            /订单\s*[：:]\s*\d+/gi,
            /order\s*[：:]\s*\d+/gi,
            /第\s*\d+\s*个?订单/gi
        ];
        
        for (const pattern of orderMarkers) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 1) {
                score += 40;
                reasons.push(`发现${matches.length}个订单标识`);
                break;
            }
        }

        // 2. 检查数字列表模式
        const numberListPattern = /^\s*\d+\s*[、.]/gm;
        const numberMatches = cleanText.match(numberListPattern);
        if (numberMatches && numberMatches.length > 1) {
            score += 30;
            reasons.push(`发现${numberMatches.length}个数字列表项`);
        }

        // 3. 检查日期时间密度
        const dateTimePatterns = [
            /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/g,
            /\d{1,2}[:：]\d{2}/g,
            /(今天|明天|后天)/g
        ];
        
        let totalDateTimeMatches = 0;
        dateTimePatterns.forEach(pattern => {
            const matches = cleanText.match(pattern) || [];
            totalDateTimeMatches += matches.length;
        });
        
        if (totalDateTimeMatches > 2) {
            score += 20;
            reasons.push(`发现${totalDateTimeMatches}个时间日期`);
        }

        // 4. 检查分隔符
        const separators = [
            /\n\s*[-=]{3,}\s*\n/g,
            /\n\s*\n\s*\n/g
        ];
        
        for (const pattern of separators) {
            const matches = cleanText.match(pattern);
            if (matches && matches.length > 0) {
                score += 15;
                reasons.push('发现明确分隔符');
                break;
            }
        }

        // 5. 文本长度和复杂度分析
        const lines = cleanText.split('\n').filter(line => line.trim().length > 10);
        if (lines.length > 8) {
            score += 10;
            reasons.push(`文本有${lines.length}行，可能包含多个订单`);
        }

        const confidence = Math.min(score / 100, 1);
        const isMultiOrder = confidence > 0.3;

        return {
            isMultiOrder,
            confidence,
            score,
            reasons: reasons.join('; ')
        };
    }

    /**
     * AI多订单检测（直接调用Gemini服务）
     * @param {string} text - 输入文本
     * @returns {Promise<object>} AI检测结果
     */
    async detectMultiOrderAI(text) {
        const logger = getLogger();
        
        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接调用Gemini服务
            const result = await geminiService.detectAndSplitMultiOrders(text);
            
            // 返回兼容的格式
            return {
                isMultiOrder: result.isMultiOrder || false,
                confidence: result.confidence || 0,
                orderCount: result.orderCount || 1,
                reason: result.analysis || '完全由Gemini AI处理'
            };
        } catch (error) {
            logger?.logError('AI多订单检测失败', error);
            return { 
                isMultiOrder: false, 
                confidence: 0, 
                orderCount: 1,
                reason: `AI检测失败: ${error.message}` 
            };
        }
    }


    /**
     * 智能分割订单文本（完全由Gemini处理）
     * @param {string} text - 输入文本
     * @returns {Promise<Array>} 分割后的订单对象数组
     */
    async smartSplitOrderText(text) {
        if (!text || typeof text !== 'string') {
            return [text || ''];
        }

        const logger = getLogger();
        
        try {
            const geminiService = getGeminiService();
            if (!geminiService) {
                throw new Error('Gemini AI服务不可用');
            }
            
            // 直接使用Gemini AI完全处理分割
            const result = await geminiService.detectAndSplitMultiOrders(text);
            
            // 更新当前分段状态 - 现在存储完整的订单对象
            this.state.currentSegments = result.orders || [text];
            
            logger?.log(`📋 Gemini智能分割完成: ${result.orders ? result.orders.length : 1} 个完整订单`, 'success', {
                isMultiOrder: result.isMultiOrder,
                confidence: result.confidence,
                analysis: result.analysis
            });
            
            return result.orders || [text];
            
        } catch (error) {
            logger?.logError('Gemini智能分割失败，返回原文本', error);
            return [text];
        }
    }

    /**
     * 显示多订单面板（适配新的浮窗结构）
     * @param {Array} orders - 完整解析的订单对象数组
     */
    showMultiOrderPanel(orders) {
        const logger = getLogger();
        logger?.log('🔄 开始显示多订单浮窗面板...', 'info');
        
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) {
            logger?.log('❌ 多订单面板元素不存在', 'error');
            return;
        }

        logger?.log(`📋 准备显示${orders.length}个订单`, 'info');

        // 存储订单数据到状态
        this.state.parsedOrders = orders;
        
        // 更新面板内容
        logger?.log('🔧 更新面板内容...', 'info');
        this.updatePanelContent(orders);
        
        // 显示浮窗面板 - 使用新的CSS类结构
        logger?.log('👁️ 显示浮窗面板...', 'info');
        multiOrderPanel.classList.remove('hidden');
        
        // 触发显示动画
        requestAnimationFrame(() => {
            multiOrderPanel.style.display = 'flex';
        });

        // 重置状态
        this.state.selectedOrders.clear();
        this.state.processedOrders.clear();
        
        // 默认选中所有订单
        orders.forEach((_, index) => {
            this.state.selectedOrders.add(index);
        });
        
        this.updateSelectedCount();
        this.updateOrderStats(orders.length);

        // 确保面板可见和居中
        this.ensurePanelVisible();

        // 启用浮窗增强功能
        this.addPanelDragFeature();
        this.addPanelToggleFeature();

        logger?.log(`✅ 多订单浮窗面板已显示，包含${orders.length}个完整订单`, 'success');
    }

    /**
     * 隐藏多订单面板（适配浮窗结构）
     */
    hideMultiOrderPanel() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            // 添加淡出动画
            multiOrderPanel.style.animation = 'multiOrderPanelHide 0.2s ease-in forwards';
            
            setTimeout(() => {
                multiOrderPanel.classList.add('hidden');
                multiOrderPanel.style.display = 'none';
                multiOrderPanel.style.animation = '';
            }, 200);
            
            getLogger()?.log('🚪 多订单浮窗面板已关闭', 'info');
        }
    }

    /**
     * 更新面板内容（显示完整解析的订单对象）- 增强版，包含批量控制面板和Paging服务检测
     * @param {Array} orders - 完整解析的订单对象数组
     */
    updatePanelContent(orders) {
        const orderList = document.querySelector('#multiOrderPanel .multi-order-list');
        if (!orderList) {
            getLogger()?.log('多订单列表容器不存在', 'warn');
            return;
        }

        // 应用Paging服务自动识别和处理
        const processedOrders = this.processPagingServiceForOrders(orders);
        
        // 更新状态中的订单数据
        this.state.parsedOrders = processedOrders;

        // 生成批量控制面板HTML
        const batchControlHTML = this.generateBatchControlPanel();
        
        // 生成订单项HTML - 显示结构化字段
        const orderItemsHTML = processedOrders.map((order, index) => {
            const orderId = `order-${index}`;
            
            // 生成订单摘要，传递正确的索引
            const summary = this.generateOrderSummary(order, index);
            
            // 检查是否为Paging订单
            const isPagingOrder = order.is_paging_order || order.carTypeId === 34;
            const pagingBadge = isPagingOrder ? '<span class="paging-badge">🏷️ 举牌</span>' : '';
            
            return `
                <div class="order-item ${isPagingOrder ? 'paging-order' : ''}" data-order-index="${index}">
                    <div class="order-header">
                        <div class="order-selector">
                            <input type="checkbox" id="${orderId}" checked>
                            <label for="${orderId}">订单 ${index + 1}</label>
                            ${pagingBadge}
                        </div>
                        <div class="order-status">
                            <span class="status-badge status-parsed">已解析</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-summary">
                            ${summary}
                        </div>
                    </div>
                    <div class="order-actions">
                        <button type="button" class="btn btn-sm btn-primary quick-edit-btn btn-mobile-optimized" 
                                onclick="window.OTA.multiOrderManager.quickEditOrder(${index})" 
                                title="编辑订单字段">
                            ✏️ 编辑
                        </button>
                        <button type="button" class="btn btn-sm btn-primary process-order-btn" 
                                onclick="window.OTA.multiOrderManager.processOrder(${index})" 
                                title="使用AI解析此订单">
                            🤖 解析
                        </button>
                        <button type="button" class="btn btn-sm btn-success preview-order-btn" 
                                onclick="window.OTA.multiOrderManager.previewOrder(${index})" 
                                title="预览订单详情" style="display: none;">
                            👁️ 预览
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        // 将批量控制面板和订单列表合并
        orderList.innerHTML = batchControlHTML + orderItemsHTML;

        // 绑定选择框事件
        const checkboxes = orderList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCount();
            });
        });

        // 绑定批量控制面板事件
        this.bindBatchControlEvents();

        // 更新统计信息
        this.updateOrderStats(processedOrders.length);
        
        // 显示Paging服务统计信息
        this.updatePagingServiceStats(processedOrders);
    }

    /**
     * 处理订单的Paging服务识别和配置
     * @param {Array} orders - 原始订单数组
     * @returns {Array} 处理后的订单数组（可能包含新生成的Paging订单）
     */
    processPagingServiceForOrders(orders) {
        const logger = getLogger();
        
        // 获取Paging服务管理器
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        if (!pagingServiceManager) {
            logger?.log('⚠️ Paging服务管理器不可用，跳过Paging服务处理', 'warn');
            return orders;
        }

        try {
            // 检测所有订单的原始文本中是否包含Paging关键词
            const allOrdersText = orders.map(order => order.rawText || '').join(' ');
            const needsPagingService = pagingServiceManager.detectPagingService(allOrdersText);
            
            if (!needsPagingService) {
                logger?.log('📋 未检测到Paging服务关键词', 'info');
                return orders;
            }

            logger?.log('🏷️ 检测到Paging服务关键词，开始处理...', 'info');

            const processedOrders = [];
            let pagingOrdersGenerated = 0;

            orders.forEach(order => {
                // 标记原订单需要Paging服务
                const updatedOrder = {
                    ...order,
                    needsPagingService: true,
                    meetAndGreet: true
                };
                processedOrders.push(updatedOrder);

                // 为接机订单生成独立的Paging订单
                if (order.subCategoryId === 2 || order.sub_category_id === 2) {
                    try {
                        const pagingOrder = pagingServiceManager.generatePagingOrder(order);
                        
                        // 转换为前端格式
                        const frontendPagingOrder = this.convertPagingOrderToFrontendFormat(pagingOrder);
                        processedOrders.push(frontendPagingOrder);
                        pagingOrdersGenerated++;
                        
                        logger?.log(`✅ 为订单 "${order.customerName || 'Unknown'}" 生成Paging订单`, 'success');
                    } catch (error) {
                        logger?.logError(`生成Paging订单失败`, error);
                    }
                }
            });

            if (pagingOrdersGenerated > 0) {
                logger?.log(`🎯 Paging服务处理完成，共生成 ${pagingOrdersGenerated} 个Paging订单`, 'success');
            }

            return processedOrders;

        } catch (error) {
            logger?.logError('Paging服务处理失败', error);
            return orders;
        }
    }

    /**
     * 将Paging订单转换为前端格式
     * @param {Object} pagingOrder - 后端格式的Paging订单
     * @returns {Object} 前端格式的Paging订单
     */
    convertPagingOrderToFrontendFormat(pagingOrder) {
        return {
            // 基础字段
            rawText: `举牌服务 - ${pagingOrder.customer_name}`,
            customerName: pagingOrder.customer_name,
            customerContact: pagingOrder.customer_contact,
            customerEmail: pagingOrder.customer_email,
            pickup: pagingOrder.pickup,
            dropoff: pagingOrder.dropoff || '举牌服务点',
            pickupDate: pagingOrder.pickup_date,
            pickupTime: pagingOrder.pickup_time,
            
            // 服务配置 - 使用camelCase格式以匹配前端
            passengerCount: pagingOrder.passenger_count || 0,
            luggageCount: pagingOrder.luggage_count || 0,
            flightInfo: pagingOrder.flight_info,
            otaReferenceNumber: pagingOrder.ota_reference_number,
            otaPrice: pagingOrder.price || 0,
            currency: pagingOrder.currency || 'MYR',
            
            // ID字段
            carTypeId: pagingOrder.car_type_id || 34, // Ticket
            subCategoryId: pagingOrder.sub_category_id || 2, // 接机
            drivingRegionId: pagingOrder.driving_region_id || 9, // Paging
            languagesIdArray: pagingOrder.languages_id_array || [5], // Paging
            
            // 特殊需求
            extraRequirement: pagingOrder.extra_requirement,
            babyChair: false,
            tourGuide: false,
            meetAndGreet: true,
            needsPagingService: true,
            
            // 标识字段
            isPagingOrder: true,
            relatedMainOrder: pagingOrder.related_main_order,
            
            // 时间信息
            arrivalTime: pagingOrder.arrival_time,
            departureTime: pagingOrder.departure_time,
            flightType: pagingOrder.flight_type
        };
    }

    /**
     * 更新Paging服务统计信息显示
     * @param {Array} orders - 订单数组
     */
    updatePagingServiceStats(orders) {
        const logger = getLogger();
        const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
        
        if (!pagingServiceManager) return;

        try {
            const stats = pagingServiceManager.getPagingServiceStats(orders);
            const summary = pagingServiceManager.createPagingServiceSummary(stats);
            
            // 在多订单面板中显示Paging服务统计
            const statsContainer = document.querySelector('.order-stats');
            if (statsContainer && stats.hasPagingService) {
                const pagingStats = document.createElement('span');
                pagingStats.className = 'paging-stats';
                pagingStats.innerHTML = `🏷️ ${summary}`;
                statsContainer.appendChild(pagingStats);
            }
            
            logger?.log(`📊 Paging服务统计: ${summary}`, 'info');
        } catch (error) {
            logger?.logError('更新Paging服务统计失败', error);
        }
    }

    /**
     * 生成批量控制面板HTML
     * @returns {string} 批量控制面板HTML
     */
    generateBatchControlPanel() {
        // 获取OTA渠道选项
        const otaChannelOptions = this.getOtaChannelOptions();
        
        // 获取语言选择组件
        const languageComponent = this.getLanguageSelectionComponent();
        
        return `
            <div class="batch-control-panel">
                <div class="batch-control-header">
                    <h4>🎛️ 批量设置</h4>
                    <button type="button" class="batch-control-toggle" onclick="window.OTA.multiOrderManager.toggleBatchControlPanel()">
                        <span class="toggle-icon">📐</span>
                    </button>
                </div>
                <div class="batch-control-content">
                    <div class="batch-control-grid">
                        <!-- OTA渠道批量设置 -->
                        <div class="batch-control-group">
                            <label class="batch-control-label">📡 OTA渠道</label>
                            <select id="batchOtaChannel" class="batch-control-select">
                                <option value="">选择OTA渠道...</option>
                                ${otaChannelOptions}
                            </select>
                            <button type="button" class="batch-apply-btn" onclick="window.OTA.multiOrderManager.applyBatchOtaChannel()">
                                应用到选中订单
                            </button>
                        </div>
                        
                        <!-- 语言批量设置 - 新的下拉菜单+多选择方式 -->
                        <div class="batch-control-group">
                            <label class="batch-control-label">🗣️ 语言设置</label>
                            ${languageComponent}
                            <button type="button" class="batch-apply-btn" onclick="window.OTA.multiOrderManager.applyBatchLanguage()">
                                应用到选中订单
                            </button>
                        </div>
                        
                        <!-- 区域批量设置 -->
                        <div class="batch-control-group">
                            <label class="batch-control-label">🌍 服务区域</label>
                            <select id="batchRegion" class="batch-control-select">
                                <option value="">选择服务区域...</option>
                                <option value="1">吉隆坡</option>
                                <option value="2">槟城</option>
                                <option value="3">新山</option>
                                <option value="4">沙巴</option>
                                <option value="5">新加坡</option>
                                <option value="9">举牌服务</option>
                                <option value="12">马六甲</option>
                            </select>
                            <button type="button" class="batch-apply-btn" onclick="window.OTA.multiOrderManager.applyBatchRegion()">
                                应用到选中订单
                            </button>
                        </div>
                        
                        <!-- 批量重置和清除 -->
                        <div class="batch-control-group batch-control-actions">
                            <button type="button" class="batch-clear-btn" onclick="window.OTA.multiOrderManager.clearBatchSettings()">
                                🗑️ 清除设置
                            </button>
                            <button type="button" class="batch-reset-btn" onclick="window.OTA.multiOrderManager.resetAllOrders()">
                                ↩️ 重置订单
                            </button>
                        </div>
                    </div>
                    
                    <!-- 批量应用状态 -->
                    <div class="batch-status">
                        <span id="batchStatusText">选择设置项并点击应用按钮来批量修改订单</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 更新订单统计信息
     * @param {number} orderCount - 订单数量
     */
    updateOrderStats(orderCount) {
        const countElement = document.getElementById('multiOrderCount');
        const dateRangeElement = document.getElementById('multiOrderDateRange');
        
        if (countElement) {
            countElement.textContent = `${orderCount} 个订单`;
        }
        
        if (dateRangeElement) {
            dateRangeElement.textContent = '待解析';
        }
    }

    /**
     * 更新选中订单数量显示
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]:checked');
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = `已选择 ${checkboxes.length} 个订单`;
        }
    }

    /**
     * 编辑指定订单
     * @param {number} index - 订单索引
     */
    editOrder(index) {
        const logger = getLogger();
        logger?.log(`编辑订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderInput = document.getElementById('orderInput');
            if (!orderInput) {
                throw new Error('订单输入框不存在');
            }

            // 将选中的订单片段填充到主输入框
            orderInput.value = this.state.currentSegments[index].trim();
            
            // 隐藏多订单面板
            this.hideMultiOrderPanel();
            
            // 显示返回多订单模式按钮
            const returnBtn = document.getElementById('returnToMultiOrder');
            if (returnBtn) {
                returnBtn.classList.remove('hidden');
                logger?.log('显示返回多订单模式按钮', 'info');
            }
            
            // 滚动到输入框并聚焦
            orderInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            orderInput.focus();
            
            // 触发输入事件以启动实时分析
            orderInput.dispatchEvent(new Event('input', { bubbles: true }));

            logger?.log(`订单 ${index + 1} 已载入编辑器`, 'success');

        } catch (error) {
            logger?.logError(`编辑订单失败: ${error.message}`, error);
            getUiManager()?.showError(`编辑订单失败: ${error.message}`);
        }
    }

    /**
     * 处理指定订单（AI解析）
     * @param {number} index - 订单索引
     */
    async processOrder(index) {
        const logger = getLogger();
        logger?.log(`开始处理订单 ${index + 1}`, 'info');

        try {
            if (index < 0 || index >= this.state.currentSegments.length) {
                throw new Error(`订单索引 ${index} 超出范围`);
            }

            const orderText = this.state.currentSegments[index].trim();
            
            // 更新UI状态
            this.updateOrderProcessingStatus(index, 'processing');

            // 调用Gemini AI分析
            const geminiService = getGeminiService();
            if (!geminiService || !geminiService.isAvailable()) {
                throw new Error('Gemini AI服务不可用');
            }

            const result = await geminiService.parseOrder(orderText);
            
            if (!result || !result.success) {
                throw new Error(result?.message || '订单解析失败');
            }

            // 存储解析结果
            this.state.processedOrders.set(index, {
                rawText: orderText,
                parsedData: result.data,
                confidence: result.confidence || 0,
                timestamp: Date.now()
            });

            // 更新应用状态
            const appState = getAppState();
            if (appState) {
                appState.setCurrentOrder({
                    rawText: orderText,
                    parsedData: result.data,
                    confidence: result.confidence || 0,
                    source: 'multi-order-single',
                    orderIndex: index
                });
            }

            // 更新UI
            this.updateOrderProcessingStatus(index, 'success');
            this.showOrderDetails(index, result.data);

            logger?.log(`订单 ${index + 1} 处理完成`, 'success', {
                confidence: result.confidence,
                dataKeys: Object.keys(result.data || {})
            });

        } catch (error) {
            logger?.logError(`处理订单 ${index + 1} 失败: ${error.message}`, error);
            this.updateOrderProcessingStatus(index, 'error', error.message);
            getUiManager()?.showError(`处理订单失败: ${error.message}`);
        }
    }

    /**
     * 预览订单详情
     * @param {number} index - 订单索引
     */
    previewOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            getUiManager()?.showError('订单未解析，请先点击解析按钮');
            return;
        }

        const detailsElement = document.querySelector(`.order-item[data-order-index="${index}"] .order-details`);
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * 更新订单处理状态
     * @param {number} index - 订单索引
     * @param {string} status - 状态：processing, success, error
     * @param {string} message - 额外信息
     */
    updateOrderProcessingStatus(index, status, message = '') {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const statusBadge = orderItem.querySelector('.status-badge');
        const processBtn = orderItem.querySelector('.process-order-btn');
        const previewBtn = orderItem.querySelector('.preview-order-btn');

        if (statusBadge) {
            statusBadge.className = 'status-badge';
            switch (status) {
                case 'processing':
                    statusBadge.classList.add('status-processing');
                    statusBadge.textContent = '解析中...';
                    break;
                case 'success':
                    statusBadge.classList.add('status-success');
                    statusBadge.textContent = '已解析';
                    break;
                case 'error':
                    statusBadge.classList.add('status-error');
                    statusBadge.textContent = '解析失败';
                    break;
                default:
                    statusBadge.classList.add('status-pending');
                    statusBadge.textContent = '待处理';
            }
        }

        if (processBtn) {
            switch (status) {
                case 'processing':
                    processBtn.disabled = true;
                    processBtn.textContent = '🔄 解析中...';
                    break;
                case 'success':
                    processBtn.disabled = false;
                    processBtn.textContent = '✅ 重新解析';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-outline');
                    break;
                case 'error':
                    processBtn.disabled = false;
                    processBtn.textContent = '🔄 重试';
                    processBtn.classList.remove('btn-primary');
                    processBtn.classList.add('btn-warning');
                    break;
                default:
                    processBtn.disabled = false;
                    processBtn.textContent = '🤖 解析';
            }
        }

        if (previewBtn && status === 'success') {
            previewBtn.style.display = 'inline-block';
        }

        if (message && status === 'error') {
            const orderContent = orderItem.querySelector('.order-content');
            if (orderContent) {
                const errorDiv = orderContent.querySelector('.error-message') || document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = `<small style="color: #dc3545;">错误: ${message}</small>`;
                if (!orderContent.contains(errorDiv)) {
                    orderContent.appendChild(errorDiv);
                }
            }
        }
    }

    /**
     * 显示订单详情
     * @param {number} index - 订单索引
     * @param {object} orderData - 解析后的订单数据
     */
    showOrderDetails(index, orderData) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const detailsElement = orderItem.querySelector('.order-details');
        if (!detailsElement) return;

        // 生成详情HTML
        const detailsHTML = `
            <div class="order-fields">
                <div class="field-group">
                    <div class="field">
                        <label>客户姓名:</label>
                        <span class="editable-field" data-field="customer_name">${orderData.customer_name || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>联系电话:</label>
                        <span class="editable-field" data-field="customer_contact">${orderData.customer_contact || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>上车地点:</label>
                        <span class="editable-field" data-field="pickup">${orderData.pickup || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>下车地点:</label>
                        <span class="editable-field" data-field="dropoff">${orderData.dropoff || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>日期:</label>
                        <span class="editable-field" data-field="pickup_date">${orderData.pickup_date || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>时间:</label>
                        <span class="editable-field" data-field="pickup_time">${orderData.pickup_time || 'N/A'}</span>
                    </div>
                </div>
                <div class="field-group">
                    <div class="field">
                        <label>乘客数:</label>
                        <span class="editable-field" data-field="passenger_count">${orderData.passenger_count || 'N/A'}</span>
                    </div>
                    <div class="field">
                        <label>服务类型:</label>
                        <span>${this.getServiceTypeName(orderData.sub_category_id)}</span>
                    </div>
                </div>
                ${orderData.flight_info ? `
                <div class="field-group">
                    <div class="field">
                        <label>航班号:</label>
                        <span>${orderData.flight_info}</span>
                    </div>
                    <div class="field">
                        <label>航班时间:</label>
                        <span>${orderData.arrival_time || orderData.departure_time || 'N/A'}</span>
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="order-actions-inline">
                <button type="button" class="btn btn-sm btn-outline" onclick="window.OTA.multiOrderManager.editOrderFields(${index})">
                    ✏️ 编辑字段
                </button>
                <button type="button" class="btn btn-sm btn-success" onclick="window.OTA.multiOrderManager.createSingleOrder(${index})">
                    🚀 创建此订单
                </button>
            </div>
        `;

        detailsElement.innerHTML = detailsHTML;
    }

    /**
     * 获取服务类型名称
     * @param {number} subCategoryId - 服务类型ID
     * @returns {string} 服务类型名称
     */
    getServiceTypeName(subCategoryId) {
        const serviceTypes = {
            2: '接机服务',
            3: '送机服务',
            4: '包车服务',
            5: '举牌服务'
        };
        return serviceTypes[subCategoryId] || '未知服务';
    }

    /**
     * 获取车型名称
     * @param {number} carTypeId - 车型ID
     * @returns {string} 车型名称
     */
    getCarTypeName(carTypeId) {
        // 从API服务中获取车型映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.carTypes) {
            const carType = apiService.staticData.carTypes.find(type => type.id === carTypeId);
            if (carType) {
                // 简化显示，只显示主要信息
                const name = carType.name;
                const simplified = name.split('(')[0].trim(); // 取括号前的部分
                return simplified;
            }
        }
        
        // 备用映射
        const carTypes = {
            5: '5座轿车',
            15: '7座MPV',
            20: '10座面包车',
            32: 'Alphard豪华车',
            34: '票务服务',
            37: '加长5座',
            38: '4座掀背'
        };
        return carTypes[carTypeId] || '标准车型';
    }

    /**
     * 获取区域名称
     * @param {number} drivingRegionId - 区域ID
     * @returns {string} 区域名称
     */
    getRegionName(drivingRegionId) {
        // 从API服务中获取区域映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.drivingRegions) {
            const region = apiService.staticData.drivingRegions.find(r => r.id === drivingRegionId);
            if (region) {
                // 简化显示，去掉括号内容
                const name = region.name;
                const simplified = name.split('(')[0].trim();
                return simplified;
            }
        }
        
        // 备用映射
        const regions = {
            1: '吉隆坡',
            2: '槟城',
            3: '新山',
            4: '沙巴',
            5: '新加坡',
            9: '举牌服务',
            12: '马六甲'
        };
        return regions[drivingRegionId] || '未知区域';
    }

    /**
     * 获取语言名称列表
     * @param {Array} languagesIdArray - 语言ID数组
     * @returns {string} 语言名称字符串
     */
    getLanguageNames(languagesIdArray) {
        if (!Array.isArray(languagesIdArray) || languagesIdArray.length === 0) {
            return '英文';
        }
        
        // 从API服务中获取语言映射
        const apiService = getApiService();
        if (apiService && apiService.staticData && apiService.staticData.languages) {
            const languageNames = languagesIdArray.map(id => {
                const language = apiService.staticData.languages.find(l => l.id === id);
                if (language) {
                    // 简化显示，只显示主要语言名
                    const name = language.name;
                    if (name.includes('(')) {
                        return name.split('(')[0].trim();
                    }
                    return name;
                }
                return null;
            }).filter(Boolean);
            
            if (languageNames.length > 0) {
                return languageNames.join(', ');
            }
        }
        
        // 备用映射
        const languages = {
            2: '英文',
            3: '马来文',
            4: '中文',
            5: '举牌',
            8: '携程司导'
        };
        
        const languageNames = languagesIdArray.map(id => languages[id] || '未知').filter(Boolean);
        return languageNames.length > 0 ? languageNames.join(', ') : '英文';
    }

    /**
     * 获取特殊需求组合
     * @param {Object} order - 订单对象
     * @returns {string} 特殊需求字符串
     */
    getSpecialRequirements(order) {
        const requirements = [];
        
        if (order.babyChair) requirements.push('儿童座椅');
        if (order.tourGuide) requirements.push('导游服务');
        if (order.meetAndGreet || order.meet_and_greet) requirements.push('迎接服务');
        if (order.extraRequirement || order.extra_requirement) {
            const extra = order.extraRequirement || order.extra_requirement;
            if (extra.trim() !== '') {
                requirements.push(extra.substring(0, 20) + (extra.length > 20 ? '...' : ''));
            }
        }
        
        return requirements.length > 0 ? requirements.join(', ') : '无';
    }

    /**
     * 编辑订单字段（内联编辑）
     * @param {number} index - 订单索引
     */
    editOrderFields(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        const editableFields = orderItem.querySelectorAll('.editable-field');
        editableFields.forEach(field => {
            const currentValue = field.textContent.trim();
            const fieldName = field.getAttribute('data-field');
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue === 'N/A' ? '' : currentValue;
            input.className = 'field-input';
            input.setAttribute('data-field', fieldName);
            
            input.addEventListener('blur', () => {
                this.saveFieldEdit(index, fieldName, input.value);
                field.textContent = input.value || 'N/A';
                field.style.display = 'inline';
                input.remove();
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                }
            });
            
            field.style.display = 'none';
            field.parentNode.insertBefore(input, field.nextSibling);
            input.focus();
        });
    }

    /**
     * 保存字段编辑
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {string} value - 新值
     */
    saveFieldEdit(index, fieldName, value) {
        const processedOrder = this.state.processedOrders.get(index);
        if (processedOrder) {
            processedOrder.parsedData[fieldName] = value;
            this.state.processedOrders.set(index, processedOrder);
            getLogger()?.log(`订单 ${index + 1} 字段 ${fieldName} 已更新`, 'info');
        }
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     */
    async createSingleOrder(index) {
        const processedOrder = this.state.processedOrders.get(index);
        if (!processedOrder) {
            getUiManager()?.showError('订单未解析，无法创建');
            return;
        }

        const logger = getLogger();
        const apiService = getApiService();
        
        if (!apiService) {
            getUiManager()?.showError('API服务不可用');
            return;
        }

        try {
            logger?.log(`开始创建订单 ${index + 1}`, 'info');
            
            // 验证订单数据
            const validation = apiService.validateOrderData(processedOrder.parsedData);
            if (!validation.isValid) {
                getUiManager()?.showValidationErrors(validation.errors);
                return;
            }

            const result = await apiService.createOrder(processedOrder.parsedData);
            
            if (result.success) {
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                logger?.log(`订单 ${index + 1} 创建成功`, 'success', { orderId });
                getUiManager()?.showSimpleSuccessToast(orderId);
                
                // 更新状态为已创建
                this.updateOrderProcessingStatus(index, 'created');
                
                // 记录到历史
                try {
                    const historyManager = window.OTA?.orderHistoryManager || window.orderHistoryManager;
                    if (historyManager) {
                        historyManager.addOrder(processedOrder.parsedData, orderId, result);
                    }
                } catch (historyError) {
                    logger?.logError('记录订单历史失败', historyError);
                }
            } else {
                const errorMessage = result.message || result.error || '订单创建失败';
                logger?.log(`订单 ${index + 1} 创建失败`, 'error', { error: errorMessage });
                getUiManager()?.showError(`订单创建失败: ${errorMessage}`);
            }
        } catch (error) {
            logger?.logError(`创建订单 ${index + 1} 异常`, error);
            getUiManager()?.showError(`创建订单异常: ${error.message}`);
        }
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeMultiOrderBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideMultiOrderPanel();
                getLogger()?.log('多订单面板已关闭', 'info');
            });
        }

        // 全选/取消全选
        const selectAllBtn = document.getElementById('selectAllOrdersBtn');
        const deselectAllBtn = document.getElementById('deselectAllOrdersBtn');
        
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllOrders(true));
        }
        
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => this.selectAllOrders(false));
        }

        // 验证全部
        const validateAllBtn = document.getElementById('validateAllOrdersBtn');
        if (validateAllBtn) {
            validateAllBtn.addEventListener('click', () => this.processAllOrders());
        }

        // 批量创建
        const batchCreateBtn = document.getElementById('batchCreateBtn');
        const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');
        
        if (batchCreateBtn) {
            batchCreateBtn.addEventListener('click', () => this.handleBatchCreate());
        }
        
        if (createSelectedBtn) {
            createSelectedBtn.addEventListener('click', () => this.createSelectedOrders());
        }

        // 点击面板外部关闭
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            multiOrderPanel.addEventListener('click', (event) => {
                if (event.target === multiOrderPanel) {
                    this.hideMultiOrderPanel();
                }
            });
        }

        // 添加面板拖拽和最小化/最大化功能
        this.addPanelDragFeature();
        this.addPanelToggleFeature();

        getLogger()?.log('多订单面板事件已初始化', 'info');
    }

    /**
     * 选择/取消选择所有订单
     * @param {boolean} selected - 是否选中
     */
    selectAllOrders(selected) {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selected;
        });
        this.updateSelectedCount();
        getLogger()?.log(`${selected ? '全选' : '取消全选'}订单`, 'info');
    }

    /**
     * 处理所有订单（批量解析）
     */
    async processAllOrders() {
        const logger = getLogger();
        logger?.log('开始批量解析所有订单', 'info');

        const orderItems = document.querySelectorAll('.order-item');
        const total = orderItems.length;
        
        this.state.batchProgress = {
            total,
            completed: 0,
            failed: 0,
            isRunning: true
        };

        this.updateBatchProgress();

        for (let i = 0; i < total; i++) {
            try {
                await this.processOrder(i);
                this.state.batchProgress.completed++;
            } catch (error) {
                logger?.logError(`批量解析订单 ${i + 1} 失败`, error);
                this.state.batchProgress.failed++;
            }
            
            this.updateBatchProgress();
            
            // 添加延迟避免API过载
            if (i < total - 1) {
                await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
            }
        }

        this.state.batchProgress.isRunning = false;
        this.updateBatchProgress();

        logger?.log(`批量解析完成，成功: ${this.state.batchProgress.completed}, 失败: ${this.state.batchProgress.failed}`, 'success');
    }

    /**
     * 更新批量进度显示
     */
    updateBatchProgress() {
        const statusElement = document.querySelector('.batch-create-status');
        if (!statusElement) return;

        const { total, completed, failed, isRunning } = this.state.batchProgress;
        
        if (isRunning) {
            statusElement.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(completed + failed) / total * 100}%"></div>
                </div>
                <div class="progress-text">
                    解析进度: ${completed + failed}/${total} (成功: ${completed}, 失败: ${failed})
                </div>
            `;
            statusElement.style.display = 'block';
        } else {
            statusElement.style.display = 'none';
        }
    }

    /**
     * 批量创建处理
     */
    async handleBatchCreate() {
        // 先解析所有订单
        await this.processAllOrders();
        
        // 然后创建所有已解析的订单
        await this.createSelectedOrders();
    }

    /**
     * 创建选中的订单
     */
    async createSelectedOrders() {
        const logger = getLogger();
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]:checked');
        
        if (checkboxes.length === 0) {
            getUiManager()?.showError('没有选中的订单');
            return;
        }

        logger?.log(`开始批量创建 ${checkboxes.length} 个选中订单`, 'info');

        let successCount = 0;
        let failedCount = 0;

        for (const checkbox of checkboxes) {
            const orderItem = checkbox.closest('.order-item');
            const index = parseInt(orderItem.getAttribute('data-order-index'));
            
            try {
                // 确保订单已解析
                if (!this.state.processedOrders.has(index)) {
                    await this.processOrder(index);
                }
                
                await this.createSingleOrder(index);
                successCount++;
            } catch (error) {
                logger?.logError(`创建订单 ${index + 1} 失败`, error);
                failedCount++;
            }
            
            // 添加延迟避免API过载
            await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
        }

        const message = `批量创建完成！成功: ${successCount}, 失败: ${failedCount}`;
        if (failedCount === 0) {
            getUiManager()?.showAlert(message, 'success');
        } else {
            getUiManager()?.showAlert(message, 'warning');
        }

        logger?.log(message, 'info');
    }

    /**
     * Chrome MCP 集成接口 - 重构版增强
     * 用于与Chrome Model Context Protocol集成测试
     */
    initChromeMCPIntegration() {
        const logger = getLogger();
        logger?.log('🌐 初始化Chrome MCP集成接口...', 'info');

        // Chrome MCP 接口对象
        this.chromeMCP = {
            // 内容提取接口
            extractContent: async (url) => {
                try {
                    logger?.log(`🔍 Chrome MCP: 尝试提取 ${url} 的内容`, 'info');
                    
                    // 检查是否在Chrome MCP环境中
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        // 实际的Chrome MCP调用将在这里实现
                        logger?.log('⚠️ Chrome MCP: 真实环境调用需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        // 模拟模式用于测试
                        logger?.log('🧪 Chrome MCP: 模拟模式运行', 'info');
                        return {
                            success: true,
                            content: '模拟提取的页面内容：订单信息...',
                            url: url,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP内容提取失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 截图接口
            captureScreenshot: async (options = {}) => {
                try {
                    logger?.log('📷 Chrome MCP: 尝试截图', 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        logger?.log('⚠️ Chrome MCP: 真实截图需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        logger?.log('🧪 Chrome MCP: 模拟截图模式', 'info');
                        return {
                            success: true,
                            screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                            options: options,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP截图失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 自动化交互接口
            automateInteraction: async (actions) => {
                try {
                    logger?.log(`🤖 Chrome MCP: 执行 ${actions.length} 个自动化操作`, 'info');
                    
                    if (typeof window.chrome !== 'undefined' && window.chrome.runtime) {
                        logger?.log('⚠️ Chrome MCP: 真实自动化需要MCP环境支持', 'warning');
                        return { success: false, message: 'Chrome MCP环境未就绪' };
                    } else {
                        logger?.log('🧪 Chrome MCP: 模拟自动化操作', 'info');
                        const results = actions.map((action, index) => ({
                            actionIndex: index,
                            type: action.type || 'unknown',
                            status: 'simulated',
                            success: true
                        }));
                        
                        return {
                            success: true,
                            results: results,
                            timestamp: Date.now()
                        };
                    }
                } catch (error) {
                    logger?.logError('Chrome MCP自动化失败', error);
                    return { success: false, error: error.message };
                }
            },

            // 检查MCP环境状态
            checkMCPStatus: () => {
                const hasChrome = typeof window.chrome !== 'undefined';
                const hasRuntime = hasChrome && !!window.chrome.runtime;
                const hasMCP = hasRuntime && !!window.chrome.runtime.sendMessage;
                
                const status = {
                    chromeAPI: hasChrome,
                    runtime: hasRuntime,
                    mcpReady: hasMCP,
                    environment: hasMCP ? 'production' : 'simulation'
                };
                
                logger?.log(`🔍 Chrome MCP状态检查:`, 'info', status);
                return status;
            }
        };

        logger?.log('✅ Chrome MCP集成接口初始化完成', 'success');
        return this.chromeMCP;
    }

    /**
     * 测试Chrome MCP集成功能
     */
    async testChromeMCPIntegration() {
        const logger = getLogger();
        logger?.log('🧪 开始测试Chrome MCP集成功能', 'info');

        try {
            // 确保MCP接口已初始化
            if (!this.chromeMCP) {
                this.initChromeMCPIntegration();
            }

            // 测试状态检查
            const status = this.chromeMCP.checkMCPStatus();
            logger?.log(`📊 MCP环境状态: ${status.environment}`, 'info');

            // 测试内容提取
            const contentResult = await this.chromeMCP.extractContent('https://example.com');
            logger?.log(`🔍 内容提取测试: ${contentResult.success ? '成功' : '失败'}`, 
                contentResult.success ? 'success' : 'warning');

            // 测试截图功能
            const screenshotResult = await this.chromeMCP.captureScreenshot({ 
                width: 800, 
                height: 600 
            });
            logger?.log(`📷 截图测试: ${screenshotResult.success ? '成功' : '失败'}`, 
                screenshotResult.success ? 'success' : 'warning');

            // 测试自动化交互
            const automationResult = await this.chromeMCP.automateInteraction([
                { type: 'click', selector: '.btn-test' },
                { type: 'input', selector: '#test-input', value: 'test' }
            ]);
            logger?.log(`🤖 自动化测试: ${automationResult.success ? '成功' : '失败'}`, 
                automationResult.success ? 'success' : 'warning');

            const testResult = {
                status: status,
                contentExtraction: contentResult.success,
                screenshot: screenshotResult.success,
                automation: automationResult.success,
                overallSuccess: true
            };

            logger?.log('🎉 Chrome MCP集成测试完成', 'success', testResult);
            return testResult;

        } catch (error) {
            logger?.logError('Chrome MCP集成测试失败', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成订单摘要信息（重构版v2.0 - 核心字段显示，支持Paging订单）
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 摘要HTML
     */
    generateOrderSummary(order, index = 0) {
        // 检查是否为Paging订单
        const isPagingOrder = order.isPagingOrder || order.is_paging_order || order.carTypeId === 34;
        
        // 核心字段提取和转换
        const customerName = order.customerName || order.customer_name || '未知客户';
        const customerContact = order.customerContact || order.customer_contact || '未提供';
        const pickup = order.pickup || '未指定';
        const dropoff = isPagingOrder ? '举牌服务点' : (order.dropoff || '未指定');
        const pickupDate = order.pickupDate || order.pickup_date || '未指定';
        const pickupTime = order.pickupTime || order.pickup_time || '未指定';
        const passengerCount = order.passengerCount || order.passenger_count || (isPagingOrder ? 0 : 1);
        const price = order.otaPrice || order.ota_price ? 
            `${order.currency || 'MYR'} ${order.otaPrice || order.ota_price}` : '未指定';
        const otaReference = order.otaReferenceNumber || order.ota_reference_number || '';
        const flightInfo = order.flightInfo || order.flight_info || '';
        
        // 服务类型+航班号组合显示（Paging订单特殊处理）
        let serviceWithFlight;
        if (isPagingOrder) {
            serviceWithFlight = flightInfo ? `举牌服务 (${flightInfo})` : '举牌服务';
        } else {
            const serviceTypeName = this.getServiceTypeName(order.subCategoryId || order.sub_category_id);
            serviceWithFlight = flightInfo ? `${serviceTypeName} (${flightInfo})` : serviceTypeName;
        }
        
        // OTA渠道名称
        const otaChannel = order.otaChannel || order.ota || order.ota_channel || '未指定';
        
        // 车型名称（Paging订单特殊处理）
        let carTypeName;
        if (isPagingOrder) {
            carTypeName = '举牌服务（无车辆）';
        } else {
            carTypeName = this.getCarTypeName(order.carTypeId || order.car_type_id);
        }
        
        // 区域名称
        const regionName = this.getRegionName(order.drivingRegionId || order.driving_region_id);
        
        // 语言名称
        const languageNames = this.getLanguageNames(order.languagesIdArray || order.languages_id_array);
        
        // 特殊需求组合（Paging订单包含meet&greet）
        const specialRequirements = isPagingOrder ? 
            '举牌迎接服务' : this.getSpecialRequirements(order);
        
        // 检测缺失的必填字段
        const missingFields = [];
        if (!customerName || customerName === '未知客户') missingFields.push('客户姓名');
        if (!customerContact || customerContact === '未提供') missingFields.push('手机号');
        if (!pickup || pickup === '未指定') missingFields.push('上车地点');
        if (!isPagingOrder && (!dropoff || dropoff === '未指定')) missingFields.push('目的地');
        if (!pickupDate || pickupDate === '未指定') missingFields.push('接送日期');
        if (!otaReference || otaReference.trim() === '') missingFields.push('OTA参考号');
        
        const hasMissingFields = missingFields.length > 0;
        const hasIncompleteOtaRef = !otaReference || otaReference.trim() === '';
        
        // Paging订单特殊样式
        const pagingClass = isPagingOrder ? 'paging-order-summary' : '';
        const pagingIcon = isPagingOrder ? '🏷️ ' : '';
        
        return `
            <div class="order-summary-grid ${pagingClass}">
                <!-- 第一行：客户信息 -->
                <div class="order-summary-item">
                    <span class="order-summary-label">${pagingIcon}客户</span>
                    <span class="order-summary-value ${!customerName || customerName === '未知客户' ? 'missing-field' : ''}">${customerName}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">手机号</span>
                    <span class="order-summary-value ${!customerContact || customerContact === '未提供' ? 'missing-field' : ''}">${customerContact}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">路线</span>
                    <span class="order-summary-value ${(!pickup || pickup === '未指定' || (!isPagingOrder && (!dropoff || dropoff === '未指定'))) ? 'missing-field' : ''}">${pickup} → ${dropoff}</span>
                </div>
                
                <!-- 第二行：时间和服务信息 -->
                <div class="order-summary-item">
                    <span class="order-summary-label">时间</span>
                    <span class="order-summary-value ${!pickupDate || pickupDate === '未指定' ? 'missing-field' : ''}">${pickupDate} ${pickupTime}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">服务类型</span>
                    <span class="order-summary-value">${serviceWithFlight}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">OTA渠道</span>
                    <span class="order-summary-value">${otaChannel}</span>
                </div>
                
                <!-- 第三行：订单详情 -->
                <div class="order-summary-item">
                    <span class="order-summary-label">OTA参考号</span>
                    <span class="order-summary-value ${hasIncompleteOtaRef ? 'missing-field' : ''}">${otaReference || '未设置'}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">车型</span>
                    <span class="order-summary-value">${carTypeName}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">区域</span>
                    <span class="order-summary-value">${regionName}</span>
                </div>
                
                <!-- 第四行：其他信息 -->
                <div class="order-summary-item">
                    <span class="order-summary-label">乘客</span>
                    <span class="order-summary-value">${isPagingOrder ? '无需车辆' : `${passengerCount}人`}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">语言</span>
                    <span class="order-summary-value">${languageNames}</span>
                </div>
                <div class="order-summary-item">
                    <span class="order-summary-label">特殊需求</span>
                    <span class="order-summary-value">${specialRequirements}</span>
                </div>
                
                <!-- 第五行：价格信息 -->
                <div class="order-summary-item">
                    <span class="order-summary-label">价格</span>
                    <span class="order-summary-value">${price}</span>
                </div>
                
                ${hasMissingFields ? `
                <!-- 警告信息跨列显示 -->
                <div class="order-summary-warning">
                    <span class="warning-icon">⚠️</span>
                    <span class="warning-text">缺少必填字段: ${missingFields.join(', ')}</span>
                </div>
                ` : ''}
                
                ${isPagingOrder ? `
                <!-- Paging订单特殊说明 -->
                <div class="order-summary-info paging-info">
                    <span class="info-icon">🏷️</span>
                    <span class="info-text">举牌服务订单 - 无需车辆，价格通常为固定费用</span>
                </div>
                ` : ''}
            </div>
        `;
    }


    /**
     * 切换订单详情显示 - 重构为直接调用快捷编辑
     * @param {number} index - 订单索引
     */
    toggleOrderDetails(index) {
        // 简化：直接调用快捷编辑，而不是切换复杂的详细字段界面
        this.quickEditOrder(index);
        
        getLogger()?.log(`订单 ${index + 1} 进入快捷编辑模式`, 'info');
    }

    /**
     * 更新订单字段值 - 增强版，确保数据同步
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    updateOrderField(index, fieldName, value) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        // 更新状态中的订单数据
        this.state.parsedOrders[index][fieldName] = value;
        
        // 执行字段验证（现在会正确找到DOM元素）
        this.validateField(index, fieldName, value);
        
        // 更新摘要显示（现在会传递正确的索引）
        this.updateOrderSummaryDisplay(index);
        
        // 如果在快捷编辑模式，确保UI同步
        const quickEditInput = document.querySelector(`.quick-edit-panel input[name="${fieldName}"]`);
        if (quickEditInput && quickEditInput.value !== value) {
            quickEditInput.value = value;
        }
        
        getLogger()?.log(`订单 ${index + 1} 的 ${fieldName} 已更新为: ${value}`, 'info');
    }

    /**
     * 验证字段值 - 修复版，支持快捷编辑模式
     * @param {number} index - 订单索引
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    validateField(index, fieldName, value) {
        // 根据当前编辑模式选择正确的DOM选择器
        let fieldGroup;
        
        // 检查是否在快捷编辑模式
        const quickEditPanel = document.querySelector('.quick-edit-overlay .quick-edit-panel');
        if (quickEditPanel) {
            // 快捷编辑模式下的选择器
            const input = quickEditPanel.querySelector(`input[name="${fieldName}"]`);
            fieldGroup = input?.closest('.order-field-group');
        } else {
            // 原有的选择器（用于其他编辑模式）
            fieldGroup = document.querySelector(`.order-item[data-order-index="${index}"] .order-field-group[data-field="${fieldName}"]`);
        }
        
        if (!fieldGroup) {
            getLogger()?.log(`找不到字段组: ${fieldName}`, 'warn');
            return;
        }

        const validationContainer = fieldGroup.querySelector('.field-validation-container');
        if (!validationContainer) return;

        // 必填字段列表
        const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
        const isRequired = requiredFields.includes(fieldName);
        const isEmpty = !value || value.toString().trim() === '';

        // 清除之前的验证状态
        fieldGroup.classList.remove('invalid', 'valid');
        validationContainer.innerHTML = '';

        if (isRequired && isEmpty) {
            // 必填字段为空
            fieldGroup.classList.add('invalid');
            validationContainer.innerHTML = `<div class="field-validation-message">此字段为必填项</div>`;
        } else if (!isEmpty) {
            // 字段有值，进行特定验证
            let isValid = true;
            let message = '';

            switch (fieldName) {
                case 'customerEmail':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailRegex.test(value);
                    message = isValid ? '邮箱格式正确' : '邮箱格式不正确';
                    break;
                case 'customerContact':
                    const phoneRegex = /^[\+]?[0-9\-\s\(\)]{8,}$/;
                    isValid = phoneRegex.test(value);
                    message = isValid ? '电话格式正确' : '电话格式不正确';
                    break;
                case 'pickupDate':
                    const dateObj = new Date(value);
                    isValid = !isNaN(dateObj.getTime()) && dateObj >= new Date().setHours(0,0,0,0);
                    message = isValid ? '日期有效' : '日期无效或已过期';
                    break;
                case 'passengerCount':
                    const count = parseInt(value);
                    isValid = !isNaN(count) && count > 0 && count <= 50;
                    message = isValid ? '乘客数量有效' : '乘客数量必须在1-50之间';
                    break;
                case 'otaPrice':
                    const price = parseFloat(value);
                    isValid = !isNaN(price) && price > 0;
                    message = isValid ? '价格有效' : '价格必须大于0';
                    break;
                default:
                    if (isRequired) {
                        message = '字段已填写';
                    }
                    break;
            }

            if (isValid && message) {
                fieldGroup.classList.add('valid');
                validationContainer.innerHTML = `<div class="field-success-message">${message}</div>`;
            } else if (!isValid) {
                fieldGroup.classList.add('invalid');
                validationContainer.innerHTML = `<div class="field-validation-message">${message}</div>`;
            }
        }
    }

    /**
     * 生成OTA参考号
     * @param {number} index - 订单索引
     */
    generateOtaReference(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const order = this.state.parsedOrders[index];
        const logger = getLogger();

        try {
            // 构建参考号：客户名字前缀 + 日期 + 随机数
            let prefix = '';
            if (order.customerName) {
                // 提取客户名字的前几个字符
                const cleanName = order.customerName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '');
                prefix = cleanName.substring(0, 3).toUpperCase();
            } else {
                prefix = 'OTA';
            }

            // 日期部分
            let datePart = '';
            if (order.pickupDate) {
                const date = new Date(order.pickupDate);
                if (!isNaN(date.getTime())) {
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    datePart = month + day;
                }
            }
            if (!datePart) {
                const today = new Date();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                datePart = month + day;
            }

            // 航班号部分（如果有）
            let flightPart = '';
            if (order.flightInfo) {
                const flightMatch = order.flightInfo.match(/[A-Z]{2}\d{3,4}/);
                if (flightMatch) {
                    flightPart = flightMatch[0];
                }
            }

            // 随机数部分
            const randomPart = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            // 组合参考号
            const otaReference = `${prefix}${datePart}${flightPart}${randomPart}`;

            // 更新字段
            this.updateOrderField(index, 'otaReferenceNumber', otaReference);

            // 更新UI中的输入框
            const input = document.querySelector(`.order-item[data-order-index="${index}"] input[name="otaReferenceNumber"]`);
            if (input) {
                input.value = otaReference;
                input.classList.remove('ota-reference-missing');
            }

            logger?.log(`为订单 ${index + 1} 生成OTA参考号: ${otaReference}`, 'success');

        } catch (error) {
            logger?.logError(`生成OTA参考号失败`, error);
            // 生成简单的随机参考号作为备用
            const simpleRef = 'OTA' + Date.now().toString().slice(-6);
            this.updateOrderField(index, 'otaReferenceNumber', simpleRef);
        }
    }

    /**
     * 快捷编辑订单
     * @param {number} index - 订单索引
     */
    quickEditOrder(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            getLogger()?.log(`无效的订单索引: ${index}`, 'warn');
            return;
        }

        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem) return;

        // 检查是否已经在编辑模式
        if (orderItem.classList.contains('editing')) {
            this.exitQuickEdit(index);
            return;
        }

        // 进入编辑模式
        orderItem.classList.add('editing');
        
        // 创建快捷编辑面板
        this.createQuickEditPanel(index);

        getLogger()?.log(`启动订单 ${index + 1} 快捷编辑模式`, 'info');
    }

    /**
     * 创建快捷编辑面板
     * @param {number} index - 订单索引
     */
    createQuickEditPanel(index) {
        // 移除现有的快捷编辑面板
        const existingPanel = document.querySelector('.quick-edit-overlay');
        if (existingPanel) {
            existingPanel.remove();
        }

        const order = this.state.parsedOrders[index];
        
        // 创建覆盖层
        const overlay = document.createElement('div');
        overlay.className = 'quick-edit-overlay';
        
        // 创建编辑面板
        const panel = document.createElement('div');
        panel.className = 'quick-edit-panel';
        
        // 常用字段的快捷编辑
        const quickFields = [
            { name: 'customerName', label: '客户姓名', type: 'text' },
            { name: 'customerContact', label: '联系电话', type: 'tel' },
            { name: 'pickup', label: '上车地点', type: 'text' },
            { name: 'dropoff', label: '目的地', type: 'text' },
            { name: 'pickupDate', label: '接送日期', type: 'date' },
            { name: 'pickupTime', label: '接送时间', type: 'time' },
            { name: 'otaReferenceNumber', label: 'OTA参考号', type: 'text' }
        ];

        let fieldsHTML = quickFields.map(field => {
            const value = order[field.name] || '';
            const isOtaRef = field.name === 'otaReferenceNumber';
            const hasValue = value && value.toString().trim() !== '';
            
            return `
                <div class="order-field-group ${!hasValue && ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'].includes(field.name) ? 'required invalid' : ''}" data-field="${field.name}">
                    <label class="order-field-label">${field.label}</label>
                    <div class="${isOtaRef ? 'ota-reference-group' : 'field-input-wrapper'}">
                        <input 
                            type="${field.type}" 
                            name="${field.name}" 
                            value="${value}" 
                            class="order-field-input ${isOtaRef && !hasValue ? 'ota-reference-missing' : ''}"
                            onchange="window.OTA.multiOrderManager.updateOrderField(${index}, '${field.name}', this.value)"
                            oninput="window.OTA.multiOrderManager.validateField(${index}, '${field.name}', this.value)">
                        ${isOtaRef && !hasValue ? `
                            <button type="button" class="ota-reference-generate-btn" 
                                    onclick="window.OTA.multiOrderManager.generateOtaReference(${index})" 
                                    title="生成随机参考号">🎲</button>
                        ` : ''}
                    </div>
                    <div class="field-validation-container"></div>
                </div>
            `;
        }).join('');

        panel.innerHTML = `
            <div class="quick-edit-header">
                <h3 class="quick-edit-title">快速编辑订单 ${index + 1}</h3>
                <button class="quick-edit-close" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">✕</button>
            </div>
            <div class="quick-edit-fields">
                ${fieldsHTML}
            </div>
            <div class="quick-edit-actions">
                <button class="btn btn-outline" onclick="window.OTA.multiOrderManager.exitQuickEdit(${index})">取消</button>
                <button class="btn btn-primary" onclick="window.OTA.multiOrderManager.saveQuickEdit(${index})">保存</button>
            </div>
        `;

        overlay.appendChild(panel);
        document.body.appendChild(overlay);

        // 显示面板动画
        requestAnimationFrame(() => {
            panel.classList.add('show');
        });

        // 点击覆盖层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.exitQuickEdit(index);
            }
        });
    }

    /**
     * 退出快捷编辑
     * @param {number} index - 订单索引
     */
    exitQuickEdit(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (orderItem) {
            orderItem.classList.remove('editing');
        }

        const overlay = document.querySelector('.quick-edit-overlay');
        if (overlay) {
            const panel = overlay.querySelector('.quick-edit-panel');
            if (panel) {
                panel.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            } else {
                overlay.remove();
            }
        }

        getLogger()?.log(`退出订单 ${index + 1} 快捷编辑模式`, 'info');
    }

    /**
     * 保存快捷编辑
     * @param {number} index - 订单索引
     */
    saveQuickEdit(index) {
        // 验证所有必填字段
        const requiredFields = ['customerName', 'pickup', 'dropoff', 'pickupDate', 'otaReferenceNumber'];
        const order = this.state.parsedOrders[index];
        let hasErrors = false;

        for (const fieldName of requiredFields) {
            const value = order[fieldName];
            if (!value || value.toString().trim() === '') {
                hasErrors = true;
                break;
            }
        }

        if (hasErrors) {
            // 显示错误提示
            const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
            if (orderItem) {
                orderItem.classList.add('invalid');
                setTimeout(() => {
                    orderItem.classList.remove('invalid');
                }, 2000);
            }
            
            getLogger()?.log(`订单 ${index + 1} 存在必填字段缺失，无法保存`, 'warn');
            return;
        }

        // 保存成功，退出编辑模式
        this.exitQuickEdit(index);
        getLogger()?.log(`订单 ${index + 1} 快捷编辑已保存`, 'success');
    }

    /**
     * 更新订单摘要显示
     * @param {number} index - 订单索引
     */
    updateOrderSummaryDisplay(index) {
        const orderItem = document.querySelector(`.order-item[data-order-index="${index}"]`);
        if (!orderItem || !this.state.parsedOrders[index]) return;

        const order = this.state.parsedOrders[index];
        const summaryDiv = orderItem.querySelector('.order-summary');
        
        if (summaryDiv) {
            // 修复：传递正确的索引参数到generateOrderSummary
            summaryDiv.innerHTML = this.generateOrderSummary(order, index);
        }
    }

    /**
     * 获取OTA渠道选项HTML
     * @returns {string} OTA渠道选项HTML
     */
    getOtaChannelOptions() {
        // 从ota-channel-mapping.js模组获取渠道数据
        const appState = getAppState();
        const currentUser = appState?.getState()?.auth?.user;
        let otaChannels = [];

        try {
            // 优先获取当前用户的特定渠道配置
            if (currentUser && window.OTA?.otaChannelMapping?.getConfig) {
                const userConfig = window.OTA.otaChannelMapping.getConfig(currentUser.id) || 
                                 window.OTA.otaChannelMapping.getConfig(currentUser.email);
                
                if (userConfig && userConfig.options) {
                    // 使用用户特定的渠道配置
                    otaChannels = userConfig.options;
                    getLogger()?.log(`使用用户特定OTA渠道配置`, 'info', { 
                        userId: currentUser.id, 
                        channelCount: otaChannels.length 
                    });
                }
            }

            // 如果没有用户特定配置，使用通用渠道列表
            if (otaChannels.length === 0 && window.OTA?.otaChannelMapping?.commonChannels) {
                otaChannels = window.OTA.otaChannelMapping.commonChannels;
                getLogger()?.log(`使用通用OTA渠道列表`, 'info', { 
                    channelCount: otaChannels.length 
                });
            }

            // 如果仍然没有数据，使用fallback列表
            if (otaChannels.length === 0) {
                otaChannels = [
                    { value: 'Ctrip', text: '携程' },
                    { value: 'Klook', text: 'Klook客路' },
                    { value: 'KKday', text: 'KKday' },
                    { value: 'Other', text: '其他' }
                ];
                getLogger()?.log(`OTA渠道模组未加载，使用fallback列表`, 'warn');
            }

        } catch (error) {
            getLogger()?.logError('获取OTA渠道配置时出错', error);
            // 错误时使用基本列表
            otaChannels = [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' },
                { value: 'Other', text: '其他' }
            ];
        }
        
        return otaChannels.map(channel => 
            `<option value="${channel.value}">${channel.text}</option>`
        ).join('');
    }

    /**
     * 获取语言选择组件HTML (下拉菜单+多选择tick box方式)
     * @returns {string} 语言选择组件HTML
     */
    getLanguageSelectionComponent() {
        const languages = [
            { id: 2, name: '英文 (English)' },
            { id: 3, name: '马来文 (Malay)' },
            { id: 4, name: '中文 (Chinese)' },
            { id: 5, name: '举牌服务 (Paging)' },
            { id: 8, name: '携程司导 (Ctrip Guide)' }
        ];
        
        return `
            <div class="language-selection-wrapper">
                <div class="language-dropdown" id="batchLanguageDropdown">
                    <div class="language-dropdown-header" onclick="window.OTA.multiOrderManager.toggleLanguageDropdown()">
                        <span id="languageSelectedText">选择语言...</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="language-dropdown-content" id="languageDropdownContent" style="display: none;">
                        ${languages.map(lang => `
                            <label class="language-checkbox-item">
                                <input type="checkbox" value="${lang.id}" onchange="window.OTA.multiOrderManager.updateLanguageSelection()">
                                <span class="checkmark"></span>
                                <span class="language-name">${lang.name}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定批量控制面板事件
     */
    bindBatchControlEvents() {
        const logger = getLogger();
        
        // 绑定批量控制面板toggle按钮
        const toggleBtn = document.querySelector('.batch-control-toggle');
        if (toggleBtn) {
            // 避免重复绑定
            toggleBtn.removeEventListener('click', this.toggleBatchControlPanel);
            toggleBtn.addEventListener('click', () => this.toggleBatchControlPanel());
        }

        // 绑定OTA渠道变化事件，实现联动效果
        const otaChannelSelect = document.getElementById('batchOtaChannel');
        if (otaChannelSelect) {
            otaChannelSelect.addEventListener('change', (e) => {
                if (e.target.value) {
                    logger?.log(`选择了OTA渠道: ${e.target.value}`, 'info');
                }
            });
        }

        // 绑定语言选择变化事件
        const languageSelect = document.getElementById('batchLanguage');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                const selectedOptions = Array.from(e.target.selectedOptions);
                if (selectedOptions.length > 0) {
                    const languages = selectedOptions.map(opt => opt.text).join(', ');
                    logger?.log(`选择了语言: ${languages}`, 'info');
                }
            });
        }

        // 为服务类型选择添加智能提示
        const serviceTypeSelect = document.getElementById('batchServiceType');
        if (serviceTypeSelect) {
            serviceTypeSelect.addEventListener('change', (e) => {
                const serviceId = parseInt(e.target.value);
                if (serviceId) {
                    const serviceName = this.getServiceTypeName(serviceId);
                    logger?.log(`选择了服务类型: ${serviceName}`, 'info');
                    
                    // 根据服务类型自动推荐合适的区域
                    this.autoSuggestRegionForService(serviceId);
                }
            });
        }

        // 为车型选择添加容量提示
        const carTypeSelect = document.getElementById('batchCarType');
        if (carTypeSelect) {
            carTypeSelect.addEventListener('change', (e) => {
                const carTypeId = parseInt(e.target.value);
                if (carTypeId) {
                    const carTypeName = this.getCarTypeName(carTypeId);
                    logger?.log(`选择了车型: ${carTypeName}`, 'info');
                }
            });
        }

        // 为区域选择添加说明
        const regionSelect = document.getElementById('batchRegion');
        if (regionSelect) {
            regionSelect.addEventListener('change', (e) => {
                const regionId = parseInt(e.target.value);
                if (regionId) {
                    const regionName = this.getRegionName(regionId);
                    logger?.log(`选择了服务区域: ${regionName}`, 'info');
                }
            });
        }

        logger?.log('✅ 批量控制面板事件绑定完成', 'success');
    }

    /**
     * 根据服务类型自动推荐合适的区域
     * @param {number} serviceId - 服务类型ID
     */
    autoSuggestRegionForService(serviceId) {
        // 举牌服务自动推荐举牌区域
        if (serviceId === 5) { // 举牌服务
            const regionSelect = document.getElementById('batchRegion');
            if (regionSelect) {
                regionSelect.value = '9'; // 举牌服务区域
                this.updateBatchStatus('已自动推荐举牌服务区域');
            }
        }
    }

    /**
     * 应用批量OTA渠道设置
     */
    applyBatchOtaChannel() {
        const select = document.getElementById('batchOtaChannel');
        const selectedChannel = select?.value;
        
        if (!selectedChannel) {
            getLogger()?.log('请先选择OTA渠道', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'ota', selectedChannel);
        });

        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置OTA渠道: ${selectedChannel}`);
        getLogger()?.log(`批量设置OTA渠道: ${selectedChannel}`, 'success', { count: selectedOrders.length });
    }

    /**
     * 应用批量语言设置
     */
    applyBatchLanguage() {
        // 从新的语言选择组件中获取选中的语言
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        const selectedLanguages = Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));
        
        if (selectedLanguages.length === 0) {
            getLogger()?.log('请先选择语言', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'languagesIdArray', selectedLanguages);
        });

        // 获取语言名称用于显示
        const languageNames = this.getSelectedLanguageNames(selectedLanguages);
        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置语言: ${languageNames.join(', ')}`);
        getLogger()?.log(`批量设置语言`, 'success', { count: selectedOrders.length, languages: selectedLanguages });
    }

    /**
     * 应用批量区域设置
     */
    applyBatchRegion() {
        const select = document.getElementById('batchRegion');
        const selectedRegion = parseInt(select?.value);
        
        if (!selectedRegion) {
            getLogger()?.log('请先选择服务区域', 'warn');
            return;
        }

        const selectedOrders = this.getSelectedOrderIndexes();
        if (selectedOrders.length === 0) {
            getLogger()?.log('请先选择要批量设置的订单', 'warn');
            return;
        }

        selectedOrders.forEach(index => {
            this.updateOrderField(index, 'drivingRegionId', selectedRegion);
        });

        const regionName = this.getRegionName(selectedRegion);
        this.updateBatchStatus(`已为 ${selectedOrders.length} 个订单设置服务区域: ${regionName}`);
        getLogger()?.log(`批量设置服务区域: ${regionName}`, 'success', { count: selectedOrders.length });
    }

    /**
     * 清除批量设置
     */
    clearBatchSettings() {
        const selects = [
            'batchOtaChannel',
            'batchLanguage', 
            'batchServiceType',
            'batchCarType',
            'batchRegion'
        ];

        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                if (select.multiple) {
                    Array.from(select.options).forEach(option => option.selected = false);
                } else {
                    select.value = '';
                }
            }
        });

        this.updateBatchStatus('批量设置已清除');
        getLogger()?.log('批量设置已清除', 'info');
    }

    /**
     * 重置所有订单
     */
    resetAllOrders() {
        if (!this.state.parsedOrders || this.state.parsedOrders.length === 0) {
            getLogger()?.log('没有可重置的订单', 'warn');
            return;
        }

        // 确认重置操作
        if (!confirm('确定要重置所有订单的编辑吗？这将撤销所有手动修改。')) {
            return;
        }

        // 这里可以实现重置逻辑，比如从原始数据恢复
        // 暂时先显示提示
        this.updateBatchStatus('订单重置功能正在开发中');
        getLogger()?.log('订单重置功能正在开发中', 'info');
    }

    /**
     * 切换批量控制面板显示/隐藏
     */
    toggleBatchControlPanel() {
        const content = document.querySelector('.batch-control-content');
        const toggleIcon = document.querySelector('.batch-control-toggle .toggle-icon');
        
        if (content && toggleIcon) {
            const isHidden = content.style.display === 'none';
            content.style.display = isHidden ? 'block' : 'none';
            toggleIcon.textContent = isHidden ? '📐' : '📊';
        }
    }

    /**
     * 获取选中订单的索引数组
     * @returns {Array<number>} 选中订单的索引数组
     */
    getSelectedOrderIndexes() {
        const checkboxes = document.querySelectorAll('.order-item input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(checkbox => {
            const orderItem = checkbox.closest('.order-item');
            return parseInt(orderItem.getAttribute('data-order-index'));
        }).filter(index => !isNaN(index));
    }

    /**
     * 更新批量状态显示
     * @param {string} message - 状态消息
     */
    updateBatchStatus(message) {
        const statusElement = document.getElementById('batchStatusText');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.style.color = '#28a745';
            
            // 3秒后恢复默认消息
            setTimeout(() => {
                statusElement.textContent = '选择设置项并点击应用按钮来批量修改订单';
                statusElement.style.color = '';
            }, 3000);
        }
    }

    /**
     * 确保面板在视窗范围内可见
     */
    ensurePanelVisible() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (!multiOrderPanel) return;

        // 确保面板居中显示在视窗中
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 获取面板的实际尺寸
        const panelRect = multiOrderPanel.getBoundingClientRect();
        
        // 如果面板超出视窗范围，调整其大小
        let adjustments = {};
        
        if (panelRect.width > viewportWidth * 0.95) {
            adjustments.width = '95vw';
        }
        
        if (panelRect.height > viewportHeight * 0.9) {
            adjustments.height = '90vh';
        }
        
        // 应用调整
        Object.assign(multiOrderPanel.style, adjustments);
        
        // 滚动到面板位置（如果需要）
        multiOrderPanel.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
        });
        
        getLogger()?.log('📱 多订单面板位置已优化', 'info', {
            viewport: `${viewportWidth}x${viewportHeight}`,
            panel: `${panelRect.width}x${panelRect.height}`,
            adjustments
        });
    }

    /**
     * 添加面板拖拽功能（增强浮窗体验）
     */
    addPanelDragFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;
        
        // 检查是否已经添加了拖拽功能，避免重复绑定
        if (header.dataset.dragEnabled === 'true') {
            getLogger()?.log('🔒 面板拖拽功能已存在，跳过重复绑定', 'info');
            return;
        }

        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;

        header.style.cursor = 'move';
        header.title = '拖拽面板来移动位置';

        const dragStart = (e) => {
            if (e.target.closest('button')) return; // 避免按钮干扰拖拽
            
            initialX = e.clientX - currentX;
            initialY = e.clientY - currentY;
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                multiOrderPanel.style.transition = 'none';
            }
        };

        const dragEnd = (e) => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            multiOrderPanel.style.transition = 'all var(--transition-normal)';
        };

        const drag = (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                // 限制拖拽范围在视窗内
                const rect = multiOrderPanel.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;
                
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));
                
                multiOrderPanel.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        };

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // 标记拖拽功能已启用
        header.dataset.dragEnabled = 'true';

        getLogger()?.log('🖱️ 面板拖拽功能已启用', 'info');
    }

    /**
     * 添加面板最小化/最大化功能
     */
    addPanelToggleFeature() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;

        // 添加最小化按钮
        const headerActions = header.querySelector('.header-actions');
        if (headerActions) {
            // 检查是否已存在切换按钮，避免重复创建
            let toggleBtn = headerActions.querySelector('#togglePanelSizeBtn');
            if (toggleBtn) {
                getLogger()?.log('📐 面板切换按钮已存在，跳过创建', 'info');
                return;
            }
            
            toggleBtn = document.createElement('button');
            toggleBtn.type = 'button';
            toggleBtn.className = 'btn btn-icon';
            toggleBtn.innerHTML = '📐';
            toggleBtn.title = '最小化/最大化';
            toggleBtn.id = 'togglePanelSizeBtn';

            let isMinimized = false;
            let originalHeight = '';

            toggleBtn.addEventListener('click', () => {
                const content = multiOrderPanel.querySelector('.multi-order-content');
                const list = multiOrderPanel.querySelector('.multi-order-list');
                const footer = multiOrderPanel.querySelector('.multi-order-footer');
                
                if (!isMinimized) {
                    // 最小化 - 只显示标题栏
                    originalHeight = multiOrderPanel.style.height || '80vh';
                    list.style.display = 'none';
                    footer.style.display = 'none';
                    multiOrderPanel.style.height = 'auto';
                    toggleBtn.innerHTML = '📊';
                    toggleBtn.title = '最大化';
                    isMinimized = true;
                } else {
                    // 最大化 - 恢复完整显示
                    list.style.display = 'block';
                    footer.style.display = 'flex';
                    multiOrderPanel.style.height = originalHeight;
                    toggleBtn.innerHTML = '📐';
                    toggleBtn.title = '最小化';
                    isMinimized = false;
                }
            });

            // 插入到关闭按钮之前
            const closeBtn = headerActions.querySelector('#closeMultiOrderBtn');
            if (closeBtn) {
                headerActions.insertBefore(toggleBtn, closeBtn);
            } else {
                headerActions.appendChild(toggleBtn);
            }
        }

        getLogger()?.log('📐 面板切换功能已启用', 'info');
    }

    /**
     * 切换语言下拉菜单显示状态
     */
    toggleLanguageDropdown() {
        const dropdown = document.getElementById('languageDropdownContent');
        if (dropdown) {
            const isVisible = dropdown.style.display !== 'none';
            dropdown.style.display = isVisible ? 'none' : 'block';
            
            // 更新箭头方向
            const arrow = dropdown.parentElement?.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.textContent = isVisible ? '▼' : '▲';
            }
        }
    }

    /**
     * 更新语言选择显示文本
     */
    updateLanguageSelection() {
        const checkboxes = document.querySelectorAll('#languageDropdownContent input[type="checkbox"]:checked');
        const selectedText = document.getElementById('languageSelectedText');
        
        if (selectedText) {
            if (checkboxes.length === 0) {
                selectedText.textContent = '选择语言...';
            } else if (checkboxes.length === 1) {
                const langName = checkboxes[0].nextElementSibling?.nextElementSibling?.textContent || '';
                selectedText.textContent = langName;
            } else {
                selectedText.textContent = `已选择 ${checkboxes.length} 种语言`;
            }
        }
    }

    /**
     * 根据语言ID获取语言名称
     * @param {number[]} languageIds - 语言ID数组
     * @returns {string[]} 语言名称数组
     */
    getSelectedLanguageNames(languageIds) {
        const languages = [
            { id: 2, name: '英文' },
            { id: 3, name: '马来文' },
            { id: 4, name: '中文' },
            { id: 5, name: '举牌服务' },
            { id: 8, name: '携程司导' }
        ];
        
        return languageIds.map(id => {
            const lang = languages.find(l => l.id === id);
            return lang ? lang.name : `语言${id}`;
        });
    }

    // ...existing code...
}

// 全局实例变量
let multiOrderManagerInstance = null;

/**
 * 创建全局实例
 * @returns {MultiOrderManager} 管理器实例
 */
function getMultiOrderManager() {
    if (!multiOrderManagerInstance) {
        multiOrderManagerInstance = new MultiOrderManager();
    }
    return multiOrderManagerInstance;
}

// 导出到全局作用域
window.MultiOrderManager = MultiOrderManager;
window.getMultiOrderManager = getMultiOrderManager;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.MultiOrderManager = MultiOrderManager;
window.OTA.getMultiOrderManager = getMultiOrderManager;
window.OTA.multiOrderManager = getMultiOrderManager();

// 向后兼容
window.multiOrderManager = getMultiOrderManager();

// 结束防重复加载检查
}
